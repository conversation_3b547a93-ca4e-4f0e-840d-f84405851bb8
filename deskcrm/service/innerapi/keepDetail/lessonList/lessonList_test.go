package lessonList

import (
	"deskcrm/api/dal"
	"deskcrm/controllers/http/innerapi/input/inputKeepDetail"
	"deskcrm/helpers"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/arkBase/lessonDataFactory"
	struArk "deskcrm/stru/ark"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"net/http"
	"net/http/httptest"
	"path"
	"runtime"
	"sync"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../../../..")

	helpers.PreInit()
	helpers.InitMysql()
	helpers.InitValidator()
	helpers.InitApiClient()
	helpers.InitRedis()
}

// testLessonListService 测试专用的服务结构体，重写了一些方法以避免外部依赖
type testLessonListService struct {
	*lessonListService
}

// 重写 GetLessonList 方法，简化逻辑以便测试
func (s *testLessonListService) GetLessonList(ctx *gin.Context) (lessonDataList *LessonListData, err error) {
	//初始化output
	output, _ := s.initOutput(ctx, s.lessonIds)

	lessonFactoryData, _ := s.getLessonDataFactoryData(ctx, s.allFilterKeys, s.lessonIds)
	_ = s.dataQueryWarming(ctx, lessonFactoryData) //dataquery字段预热

	lessonFactoryData, _ = s.getLessonDataFactoryData(ctx, s.sortKeys, s.lessonIds)
	_ = lessonDataFactory.FormatRouter.Exec(ctx, s.dataQueryPoint, lessonFactoryData, output)

	lessonList, err := s.outputToSliceAndSort(ctx, output)
	if err != nil {
		zlog.Errorf(ctx, "outputToSliceAndSort err:%s", err.Error())
	}

	lessonIDs := make([]int64, 0)
	for _, lessonDetail := range lessonList {
		lessonID := cast.ToInt64(lessonDetail["lessonId"])
		lessonIDs = append(lessonIDs, lessonID)
	}
	_ = s.resetLessonList(ctx, lessonIDs)

	//取数据只取数据字段key
	lessonFactoryData, _ = s.getLessonDataFactoryData(ctx, s.allFilterKeys, lessonIDs)
	output, _ = s.initOutput(ctx, lessonIDs) //初始化output,获取列表数据

	_ = lessonDataFactory.FormatRouter.Exec(ctx, s.dataQueryPoint, lessonFactoryData, output)

	lessonList, err = s.outputToSliceAndSort(ctx, output)
	if err != nil {
		zlog.Errorf(ctx, "outputToSliceAndSort err:%s", err.Error())
	}

	lessonList = s.addDataSourceComment(ctx, lessonList, output.LessonListSource)

	return &LessonListData{
		LessonList: lessonList,
		lessonIDs:  s.lessonIds,
		Total:      len(s.lessonMap),
	}, nil
}

func (s *testLessonListService) InitLessonListConfig(ctx *gin.Context, rules []*struLessonList.LessonRuleConfigStru) (err error) {
	keys := make([]string, 0)
	fieldRuleMap := map[string]*struLessonList.LessonRuleConfigStru{}
	for _, ruleConfig := range rules {
		keys = append(keys, ruleConfig.Key)
		fieldRuleMap[ruleConfig.Key] = ruleConfig
	}

	s.lock.Lock()
	s.fieldRuleMap = fieldRuleMap
	s.allFilterKeys = keys
	s.GroupKey = s.param.Tab
	s.lock.Unlock()
	return
}

// createTestLessonListService 创建测试用的 testLessonListService 实例
func createTestLessonListService() *testLessonListService {
	baseService := &lessonListService{
		param:              &inputKeepDetail.LessonListParam{},
		lessonIds:          make([]int64, 0),
		lessonMap:          map[int64]*dal.LessonInfo{},
		fieldRuleMap:       map[string]*struLessonList.LessonRuleConfigStru{},
		implodeStudentUids: make([]int64, 0),
		sorts:              make([]*struArk.SortsRule, 0),
		lock:               &sync.Mutex{},
	}
	return &testLessonListService{
		lessonListService: baseService,
	}
}

func createCtx() *gin.Context {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	req, _ := http.NewRequest("GET", "/", nil)
	req.AddCookie(&http.Cookie{
		Name:  "ZYBIPSCAS",
		Value: "IPS_e13a2d2419c79f6819022eeca4f6a2f01754639315",
	})
	ctx.Request = req
	return ctx
}

func initService(ctx *gin.Context, param *inputKeepDetail.LessonListParam, rules []*struLessonList.LessonRuleConfigStru) (leesonListPoint *testLessonListService, err error) {
	// 创建服务实例
	leesonListPoint = createTestLessonListService()

	dataQueryPoint := dataQuery.New()
	err = leesonListPoint.InitDataQueryPoint(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	err = leesonListPoint.InitParam(ctx, param)
	if err != nil {
		return
	}

	err = leesonListPoint.InitLessonList(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	err = leesonListPoint.InitLessonListConfig(ctx, rules)
	if err != nil {
		return
	}

	return
}

func createCommonParam() *inputKeepDetail.LessonListParam {
	return &inputKeepDetail.LessonListParam{
		AssistantUid: 4635163083,
		PersonUid:    3000332147,
		CourseId:     3740621,
		StudentUid:   4452570490,
		LeadsId:      4452570490,
	}
}

func doTest(t *testing.T, param *inputKeepDetail.LessonListParam, rules []*struLessonList.LessonRuleConfigStru, skipCheckEmpty bool) {
	ctx := createCtx()

	leesonListPoint, err := initService(ctx, param, rules)
	assert.NoError(t, err)

	lessonDataList, err := leesonListPoint.GetLessonList(ctx)
	assert.NoError(t, err)

	for _, lessonData := range lessonDataList.LessonList {
		for _, rule := range rules {
			if value, ok := lessonData[rule.Key]; ok {
				if !skipCheckEmpty {
					assert.NotEmpty(t, value)
				}
			} else {
				assert.Fail(t, "field not found: "+rule.Key)
			}
		}
	}

	t.Logf("result: %+v", lessonDataList)
}

func TestLessonListService_GetLessonList_lessonId(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "lessonId",
			Function: "GetLessonId",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_type(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "type",
			Function: "GetType",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_playType(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "playType",
			Function: "GetPlayType",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_inclassTime(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "inclassTime",
			Function: "GetInclassTime",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_stopTime(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "stopTime",
			Function: "GetStopTime",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_lessonName(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "lessonName",
			Function: "GetLessonName",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_startTime(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "startTime",
			Function: "GetStartTime",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_preview(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "preview",
			Function: "GetPreview",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_attend(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "attend",
			Function: "GetAttendData",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_playback(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "playback",
			Function: "GetPlayback",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_playbackv1(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "playbackv1",
			Function: "GetPlaybackOnlineTimeV1",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_lbpAttendDuration(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "lbpAttendDuration",
			Function: "GetLbpAttendDuration",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_lbpAttendDurationOld(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "lbpAttendDurationOld",
			Function: "GetLbpAttendDurationOld",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_inclassTest(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "inclassTest",
			Function: "GetInclassTest",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_oralQuestion(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "oralQuestion",
			Function: "GetOralQuestion",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_homework(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "homework",
			Function: "GetHomeworkData",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_similarHomework(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "similarHomework",
			Function: "GetHomeworkLikeData",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_exercise(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "exercise",
			Function: "GetExerciseColumn",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_exerciseAll(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "exerciseAll",
			Function: "GetExerciseAllColumn",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_lbpInteractExam(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "lbpInteractExam",
			Function: "GetLbpInteractExamColumn",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_mixPlaybackInteract(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "mixPlaybackInteract",
			Function: "GetMixPlaybackInteract",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_littleKidFudaoHomeworkStatus(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "littleKidFudaoHomeworkStatus",
			Function: "GetLittleKidFudaoData",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_littleKidFudaoHomeworkLevel(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "littleKidFudaoHomeworkLevel",
			Function: "GetLittleKidFudaoData",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, true)
}

func TestLessonListService_GetLessonList_littleKidFudaoInteract(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "littleKidFudaoInteract",
			Function: "GetLittleKidInteractData",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}

func TestLessonListService_GetLessonList_synchronousPractice(t *testing.T) {
	rules := []*struLessonList.LessonRuleConfigStru{
		{
			Key:      "synchronousPractice",
			Function: "GetSynchronousPractice",
		},
	}
	param := createCommonParam()
	doTest(t, param, rules, false)
}
